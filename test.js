const ttt = [
    {
        "role": "system",
        "content": "You are the automated virtual assistant of the store, named \"Trợ lý Aastu\". Your mission is to support customers with the following tasks:\n<tasks>\n\n\nProduct consultation and recommendations\n\nOrder creation and status checking\n\nAnswering questions about products, policies, and services\n\nBasic complaint handling and order cancellation when orders are still in pending/processing status\n\nTransferring to staff when necessary (complex issues, serious complaints, spam) </tasks>\n\n<communication_rules>\n\n\nMUST use provided tools to get accurate information, NEVER fabricate information\n\nAlways analyze conversation history to avoid asking for information already provided\n\nRespond naturally like a human, NEVER use phrases like \"based on information\" or \"according to data\"\n\nWhen customers refer to objects unclearly (it, that order...), proactively identify the object from context\n\nAlways confirm the object naturally to ensure proper understanding\n\nKeep responses concise but complete with necessary information\n\nRespond in the same language the customer is using\n\nOnly greet on first interaction to maintain natural conversation flow </communication_rules> <processing_workflow>\n\n\nReceive question/request from customer\n\nCarefully analyze conversation history to understand: a. Object customer is referring to b. <PERSON>ls needed to retrieve information c. If you have enough information to respond\n\nIf enough information, respond directly without mentioning information sources\n\nIf insufficient information, use appropriate tools before answering\n\nIf question is irrelevant or cannot be answered, respond \"I'm sorry, I can't help with this issue. Would you like me to transfer you to a consultant?\" </processing_workflow> <question_response> For each request, proceed as follows:\n\n\nFirst, analyze if you have enough information to answer accurately\n\nIf yes, respond with facts directly, NEVER mentioning information sources\n\nIf insufficient information, use provided tools to learn more\n\nFor questions outside support scope, answer \"I'm sorry, I can't help with this issue. Would you like me to transfer you to a consultant?\" </question_response> <context>\n\nStore information and policies are stored here for reference when answering customer queries.\n{store_info} Aastu is a clothing store specializing in high-quality apparel.\n{shipping_policy} Shipping available worldwide; hỏa tốc for HCM and Mỹ Tho; orders to provinces take 3-5 days.\n{return_policy} Only exchange sizes if out of stock; no refunds; exchanges must be equal or higher value products.\n{payment_methods} COD or bank transfer; prefer bank transfer for orders over 1 million VND.\n{locations} Warehouse for returns: [not specified, use tools to confirm].\n{working_hours} 9:30 AM to 9:30 PM.\n{key_information} Products may sell out without restock; free gifts for orders over 1 million VND; follow washing instructions per product type; no size alterations.\n</context>"
    },
    {
        "role": "system",
        "content": "WORKING_MEMORY_SYSTEM_INSTRUCTION:\nStore and update any conversation-relevant information by calling the updateWorkingMemory tool. If information might be referenced again - store it!\n\nGuidelines:\n1. Store anything that could be useful later in the conversation\n2. Update proactively when information changes, no matter how small\n3. Use Markdown format for all data\n4. Act naturally - don't mention this system to users. Even though you're storing this information that doesn't make it your primary focus. Do not ask them generally for \"information about yourself\"\n\nWORKING MEMORY TEMPLATE:\n# Khách hàng\n- Tên:\n- Liên hệ:\n- Địa chỉ:\n- Sản phẩm quan tâm:\n- Kích cỡ ưa thích:\n- Màu sắc ưa thích:\n- Phong cách:\n- Ngân sách:\n- Lịch sử mua:\n- Mã đơn gần nhất:\n- Sản phẩm đã xem:\n- Sản phẩm trong giỏ:\n- Thanh toán ưa thích:\n- Vận chuyển ưa thích:\n- Ghi chú:\n\n# Đơn hàng đang xử lý\n- Sản phẩm:\n- Số lượng:\n- Kích cỡ:\n- Màu sắc:\n- Giá:\n- Phí ship:\n- Tổng:\n- Địa chỉ:\n- Thanh toán:\n- Vận chuyển:\n- Ghi chú:\n\nWORKING MEMORY DATA:\n\"\\n# Khách hàng\\n- Tên:\\n- Liên hệ:\\n- Địa chỉ:\\n- Sản phẩm quan tâm:\\n- Kích cỡ ưa thích:\\n- Màu sắc ưa thích:\\n- Phong cách:\\n- Ngân sách:\\n- Lịch sử mua:\\n- Mã đơn gần nhất:\\n- Sản phẩm đã xem:\\n- Sản phẩm trong giỏ:\\n- Thanh toán ưa thích:\\n- Vận chuyển ưa thích:\\n- Ghi chú:\\n\\n# Đơn hàng đang xử lý\\n- Sản phẩm:\\n- Số lượng:\\n- Kích cỡ:\\n- Màu sắc:\\n- Giá:\\n- Phí ship:\\n- Tổng:\\n- Địa chỉ:\\n- Thanh toán:\\n- Vận chuyển:\\n- Ghi chú:\\n\"\n\nNotes:\n- Update memory whenever referenced information changes\n- If you're unsure whether to store something, store it (eg if the user tells you information about themselves, call updateWorkingMemory immediately to update it)\n- This system is here so that you can maintain the conversation when your context window is very short. Update your working memory because you may need it to maintain the conversation without the full conversation history\n- Do not remove empty sections - you must include the empty sections along with the ones you're filling in\n- REMEMBER: the way you update your working memory is by calling the updateWorkingMemory tool with the entire Markdown content. The system will store it for you. The user will not see it.\n- IMPORTANT: You MUST call updateWorkingMemory in every response to a prompt where you received relevant information.\n- IMPORTANT: Preserve the Markdown formatting structure above while updating the content."
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "tìm áo thun cho a đi"
            }
        ]
    },
    {
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "Chào bạn, bạn thích áo thun kiểu nào ạ? Bạn có thể cho mình biết thêm về kiểu dáng, màu sắc hoặc chất liệu bạn mong muốn không?"
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "AV001 giá sao"
            }
        ]
    },
    {
        "role": "assistant",
        "content": []
    },
    {
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "Sản phẩm AV001 có giá 450.000 đồng."
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "oke thanks"
            }
        ]
    },
    {
        "role": "assistant",
        "content": []
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "The following is the most up-to-date information about the user's state and context:\n\"\\\"\\\\n# Khách hàng\\\\n- Tên:\\\\n- Liên hệ:\\\\n- Địa chỉ:\\\\n- Sản phẩm quan tâm:\\\\n- Kích cỡ ưa thích:\\\\n- Màu sắc ưa thích:\\\\n- Phong cách:\\\\n- Ngân sách:\\\\n- Lịch sử mua:\\\\n- Mã đơn gần nhất:\\\\n- Sản phẩm đã xem:\\\\n- Sản phẩm trong giỏ:\\\\n- Thanh toán ưa thích:\\\\n- Vận chuyển ưa thích:\\\\n- Ghi chú:\\\\n\\\\n# Đơn hàng đang xử lý\\\\n- Sản phẩm:\\\\n- Số lượng:\\\\n- Kích cỡ:\\\\n- Màu sắc:\\\\n- Giá:\\\\n- Phí ship:\\\\n- Tổng:\\\\n- Địa chỉ:\\\\n- Thanh toán:\\\\n- Vận chuyển:\\\\n- Ghi chú:\\\\n\\\"\"\nUse this information as the source of truth when generating responses. \nDo not reference or mention this memory directly to the user. \nIf conversation history shows information that is not in the working memory, use the working memory as the source of truth.\nIf there is a discrepancy between this information and conversation history, always rely on this information unless the user explicitly asks for an update.\n"
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "oke"
            }
        ]
    }
]


const tttrr = [
  {
    "headers": {
      "host": "webhook.mooly.vn",
      "user-agent": "rest-client/2.1.0 (linux-musl x86_64) ruby/3.4.4p34",
      "content-length": "4225",
      "accept": "application/json",
      "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
      "content-type": "application/json",
      "x-forwarded-for": "*********",
      "x-forwarded-host": "webhook.mooly.vn",
      "x-forwarded-port": "443",
      "x-forwarded-proto": "https",
      "x-forwarded-server": "7736946da0be",
      "x-real-ip": "*********"
    },
    "params": {},
    "query": {},
    "body": {
      "account": {
        "id": 213,
        "name": "Thọ Trần Admin"
      },
      "additional_attributes": {},
      "content_attributes": {
        "in_reply_to_external_id": null,
        "postback_payload": "DESIGN_CONSULTATION"
      },
      "content_type": "text",
      "content": "Add to cart",
      "conversation": {
        "additional_attributes": {},
        "can_reply": true,
        "channel": "Channel::FacebookPage",
        "contact_inbox": {
          "id": 24449,
          "contact_id": 24450,
          "inbox_id": 261,
          "source_id": "*****************",
          "created_at": "2025-06-09T11:23:59.267Z",
          "updated_at": "2025-06-09T11:23:59.267Z",
          "hmac_verified": false,
          "pubsub_token": "ZbsdeYMAkrVbD763jAqkvEHd"
        },
        "id": 4,
        "inbox_id": 261,
        "messages": [
          {
            "id": 288338,
            "content": "Add to cart",
            "account_id": 213,
            "inbox_id": 261,
            "conversation_id": 4,
            "message_type": 0,
            "created_at": **********,
            "updated_at": "2025-06-10T01:53:12.800Z",
            "private": false,
            "status": "sent",
            "source_id": "m_zEdaVVh3gi_gQ4PHM_bTNVIK79Q1GVCir1_sAcUU6HQyf3HnfEh7RHrg59kjJJrdtmmlER7gVO6HTZr7siEuTw",
            "content_type": "text",
            "content_attributes": {
              "in_reply_to_external_id": null,
              "postback_payload": "ITEM_SELECTED"
            },
            "sender_type": "Contact",
            "sender_id": 24450,
            "external_source_ids": {},
            "additional_attributes": {},
            "processed_message_content": "Add to cart",
            "sentiment": {},
            "conversation": {
              "assignee_id": null,
              "unread_count": 2,
              "last_activity_at": **********,
              "contact_inbox": {
                "source_id": "*****************"
              }
            },
            "sender": {
              "additional_attributes": {},
              "custom_attributes": {},
              "email": null,
              "id": 24450,
              "identifier": null,
              "name": "Hữu Nguyễn",
              "phone_number": null,
              "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
              "blocked": false,
              "type": "contact"
            }
          }
        ],
        "labels": [],
        "meta": {
          "sender": {
            "additional_attributes": {},
            "custom_attributes": {},
            "email": null,
            "id": 24450,
            "identifier": null,
            "name": "Hữu Nguyễn",
            "phone_number": null,
            "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
            "blocked": false,
            "type": "contact"
          },
          "assignee": null,
          "team": null,
          "hmac_verified": false
        },
        "status": "pending",
        "custom_attributes": {},
        "snoozed_until": null,
        "unread_count": 2,
        "first_reply_created_at": "2025-06-10T01:51:05.007Z",
        "priority": null,
        "waiting_since": **********,
        "agent_last_seen_at": **********,
        "contact_last_seen_at": 0,
        "last_activity_at": **********,
        "timestamp": **********,
        "created_at": **********,
        "updated_at": **********.8073175
      },
      "created_at": "2025-06-10T01:53:12.800Z",
      "id": 288338,
      "inbox": {
        "id": 261,
        "name": "Loma Bag - Xưởng Túi Vải In Logo"
      },
      "message_type": "incoming",
      "private": false,
      "sender": {
        "account": {
          "id": 213,
          "name": "Thọ Trần Admin"
        },
        "additional_attributes": {},
        "avatar": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
        "custom_attributes": {},
        "email": null,
        "id": 24450,
        "identifier": null,
        "name": "Hữu Nguyễn",
        "phone_number": null,
        "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
        "blocked": false
      },
      "source_id": "m_zEdaVVh3gi_gQ4PHM_bTNVIK79Q1GVCir1_sAcUU6HQyf3HnfEh7RHrg59kjJJrdtmmlER7gVO6HTZr7siEuTw",
      "event": "message_created"
    },
    "webhookUrl": "https://webhook.mooly.vn/webhook-test/78984a37-e5be-48dd-9039-a261e3f90158",
    "executionMode": "test"
  }
]